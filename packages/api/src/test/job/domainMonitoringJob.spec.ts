import { SinonStub, stub, restore } from "sinon";
import { expect, should, use } from "chai";
import { factory } from "factory-girl";
import { complexStructure, createComplexStructure, truncate } from "../entities/helper";
import { FACTORY } from "../factories/common";
import { DomainMonitoringJob } from "../../skywind/jobs/domainMonitoringJob";
import { MonitoredDomain, DomainStatus } from "../../skywind/services/domainDetector/types";
import * as EntityService from "../../skywind/services/entity";
import { EntityStatus } from "../../skywind/entities/entity";
import * as httpClientModule from "../../skywind/utils/httpClient";
import config from "../../skywind/config";

should();
use(require("chai-as-promised"));

class MockHttpClient {
    private domains = new Map<string, MonitoredDomain>();

    async get<T>(url: string): Promise<T> {
        if (url === "") {
            // List all domains
            return Array.from(this.domains.values()) as T;
        } else {
            // Get specific domain
            const domain = decodeURIComponent(url.replace("/", ""));
            const monitoredDomain = this.domains.get(domain);
            if (!monitoredDomain) {
                throw new Error(`Domain ${domain} not found`);
            }
            return monitoredDomain as T;
        }
    }

    async post<T>(_url: string, data: { domain: string }): Promise<T> {
        const monitoredDomain: MonitoredDomain = {
            domain: data.domain,
            status: { accessStatus: "UNKNOWN" }
        };
        this.domains.set(data.domain, monitoredDomain);
        return monitoredDomain as T;
    }

    async delete(url: string): Promise<void> {
        const domain = decodeURIComponent(url.replace("/", ""));
        this.domains.delete(domain);
    }

    // Helper methods for testing
    setDomainStatus(domain: string, status: DomainStatus) {
        const monitoredDomain = this.domains.get(domain);
        if (monitoredDomain) {
            monitoredDomain.status = status;
        }
    }

    getDomains(): string[] {
        return Array.from(this.domains.keys());
    }

    clear() {
        this.domains.clear();
    }
}

describe("Domain Monitoring Job", () => {
    let createHttpClientStub: SinonStub;
    let httpClient: MockHttpClient;
    let masterEntity: any;
    let loggingOutput: any;

    before(async () => {
        loggingOutput = config.loggingOutput;
        config.loggingOutput = "console";
        await truncate();
        await createComplexStructure();

        masterEntity = await EntityService.findOne({ key: complexStructure.masterKey });
    });

    beforeEach(() => {
        createHttpClientStub = stub(httpClientModule, "createHttpClient");
        httpClient = new MockHttpClient();
        createHttpClientStub.returns(httpClient);
    });

    afterEach(() => {
        restore();
        httpClient.clear();
    });

    after(() => {
        config.loggingOutput = loggingOutput;
    });

    it("should process domains from static domain pools", async () => {
        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-static-pool",
            domainDetectorAdapterId: "tapking",
            domains: ["static1.example.com", "static2.example.com"]
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.include("static1.example.com");
        expect(httpClient.getDomains()).to.include("static2.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });

    it("should process domains from dynamic domain pools", async () => {
        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-dynamic-pool",
            domainDetectorAdapterId: "tapking",
            domains: [
                { domain: "dynamic1.example.com", environment: "test" },
                { domain: "dynamic2.example.com", environment: "test" }
            ]
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            dynamicDomainPoolId: dynamicDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.include("dynamic1.example.com");
        expect(httpClient.getDomains()).to.include("dynamic2.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });

    it("should handle blocked domains and call setBlocked on sources", async () => {
        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-pool",
            domainDetectorAdapterId: "tapking",
            domains: ["blocked.example.com"]
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        httpClient.setDomainStatus("blocked.example.com", {
            accessStatus: "BLOCKED",
            lastCheckedAt: new Date().toISOString()
        });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.not.include("blocked.example.com");
    });

    it("should handle multiple domain pools with same adapter", async () => {
        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "static-pool",
            domainDetectorAdapterId: "tapking",
            domains: ["static.example.com"]
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "dynamic-pool",
            domainDetectorAdapterId: "tapking",
            domains: [{ domain: "dynamic.example.com", environment: "test" }]
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            dynamicDomainPoolId: dynamicDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.include("static.example.com");
        expect(httpClient.getDomains()).to.include("dynamic.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });

    it("should handle empty domain list", async () => {
        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.have.length(0);
    });

    it("should handle domains with different statuses correctly", async () => {
        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-status-pool",
            domainDetectorAdapterId: "tapking",
            domains: ["available.example.com", "unknown.example.com"]
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        httpClient.setDomainStatus("available.example.com", {
            accessStatus: "AVAILABLE",
            lastCheckedAt: new Date().toISOString()
        });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(httpClient.getDomains()).to.include("available.example.com");
        expect(httpClient.getDomains()).to.include("unknown.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });

    it("should reuse detector instances for the same adapter", async () => {
        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-reuse-pool",
            domainDetectorAdapterId: "tapking",
            domains: ["domain1.example.com", "domain2.example.com"]
        });

        await factory.create(FACTORY.MERCHANT_ENTITY, {
            staticDomainPoolId: staticDomainPool.id,
            status: EntityStatus.NORMAL
        }, { parent: masterEntity });

        const job = new DomainMonitoringJob();
        await job.fire();

        expect(createHttpClientStub.called).to.be.true;

        expect(httpClient.getDomains()).to.include("domain1.example.com");
        expect(httpClient.getDomains()).to.include("domain2.example.com");
        expect(httpClient.getDomains()).to.have.length(2);
    });
});
