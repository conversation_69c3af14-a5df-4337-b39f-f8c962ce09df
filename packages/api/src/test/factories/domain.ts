import { lazy } from "@skywind-group/sw-utils";
import { DomainStatus, StaticDomainType } from "../../skywind/entities/domain";
import { FACTORY } from "./common";
import { Models } from "../../skywind/models/models";
import { getStaticDomainPoolService } from "../../skywind/services/staticDomainPool";
import { getDynamicDomainPoolService } from "../../skywind/services/dynamicDomainPool";

const factory = require("factory-girl").factory;

interface DomainBuildOptions {
    domain?: string;
}

interface DynamicBuildOptions extends DomainBuildOptions {
    environment?: string;
}

interface StaticDomainPoolBuildOptions {
    name?: string;
    domainDetectorAdapterId?: string;
    domains?: string[];
}

interface DynamicDomainPoolBuildOptions {
    name?: string;
    domainDetectorAdapterId?: string;
    domains?: Array<{ domain: string; environment?: string }>;
}

export const defineDomainFactory = lazy(() => {
    factory.define(FACTORY.STATIC_DOMAIN, Models.StaticDomainModel, (buildOptions: DomainBuildOptions) => {
        return {
            domain: buildOptions.domain || factory.chance("domain"),
            status: DomainStatus.ACTIVE,
            type: StaticDomainType.STATIC
        };
    });

    factory.define(FACTORY.DYNAMIC_DOMAIN, Models.DynamicDomainModel, (buildOptions: DynamicBuildOptions) => {
        return {
            domain: buildOptions.domain || factory.chance("domain"),
            environment: buildOptions.environment || factory.chance("word"),
            status: DomainStatus.ACTIVE
        };
    });

    factory.define(FACTORY.STATIC_DOMAIN_POOL, Models.StaticDomainPoolModel, (buildOptions: StaticDomainPoolBuildOptions) => {
        return {
            name: buildOptions.name || factory.chance("word"),
            domainDetectorAdapterId: buildOptions.domainDetectorAdapterId || null
        };
    });

    // After create hook for STATIC_DOMAIN_POOL to add domains
    factory.afterCreate(FACTORY.STATIC_DOMAIN_POOL, async (staticDomainPool: any, attrs: StaticDomainPoolBuildOptions) => {
        if (attrs.domains && attrs.domains.length > 0) {
            const staticDomainPoolService = getStaticDomainPoolService();

            for (const domainName of attrs.domains) {
                // Create the static domain
                const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN, { domain: domainName });

                // Add it to the pool
                await staticDomainPoolService.addDomain(staticDomainPool.id, staticDomain.id);
            }
        }
        return staticDomainPool;
    });

    factory.define(FACTORY.DYNAMIC_DOMAIN_POOL, Models.DynamicDomainPoolModel, (buildOptions: DynamicDomainPoolBuildOptions) => {
        return {
            name: buildOptions.name || factory.chance("word"),
            domainDetectorAdapterId: buildOptions.domainDetectorAdapterId || null
        };
    });

    // After create hook for DYNAMIC_DOMAIN_POOL to add domains
    factory.afterCreate(FACTORY.DYNAMIC_DOMAIN_POOL, async (dynamicDomainPool: any, attrs: DynamicDomainPoolBuildOptions) => {
        if (attrs.domains && attrs.domains.length > 0) {
            const dynamicDomainPoolService = getDynamicDomainPoolService();

            for (const domainConfig of attrs.domains) {
                // Create the dynamic domain
                const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
                    domain: domainConfig.domain,
                    environment: domainConfig.environment || "test"
                });

                // Add it to the pool
                await dynamicDomainPoolService.addDomain(dynamicDomainPool.id, dynamicDomain.id);
            }
        }
        return dynamicDomainPool;
    });

    return factory;
});
