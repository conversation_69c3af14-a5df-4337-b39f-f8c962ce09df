import { expect } from "chai";
import { loadDomains } from "../../../skywind/services/domainDetector/loadDomains";
import { truncate } from "../../entities/helper";
import { FACTORY } from "../../factories/common";
import { EntityStatus } from "../../../skywind/entities/entity";

const factory = require("factory-girl").factory;

describe("loadDomains", () => {
    before(async () => {
        await truncate();
    });

    it("should load domains from static domain pools", async () => {
        await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static-pool-domain.com"
        });
        const staticEntityDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "static-entity-domain.com"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-static-pool",
            domainDetectorAdapterId: "tapking",
            domains: ["static-pool-domain.com"]
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            staticDomainId: staticEntityDomain.id,
            staticDomainPoolId: staticDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("static-pool-domain.com")).to.be.true;
        expect(tapkingDomains?.has("static-entity-domain.com")).to.be.true;
    });

    it("should load domains from dynamic domain pools", async () => {
        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-dynamic-pool",
            domainDetectorAdapterId: "tapking",
            domains: [{ domain: "dynamic-pool-domain.com", environment: "test" }]
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("dynamic-pool-domain.com")).to.be.true;
    });

    it("should load individual dynamic domains with pool adapter", async () => {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "individual-dynamic-domain.com",
            environment: "test"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-pool-for-adapter",
            domainDetectorAdapterId: "custom-adapter"
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            dynamicDomainId: dynamicDomain.id,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("custom-adapter")).to.be.true;

        const customAdapterDomains = result.get("custom-adapter");
        expect(customAdapterDomains).to.be.instanceOf(Map);
        expect(customAdapterDomains?.has("individual-dynamic-domain.com")).to.be.true;
    });

    it("should not load individual dynamic domains without pool adapter", async () => {
        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "orphaned-dynamic-domain.com",
            environment: "test"
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            dynamicDomainId: dynamicDomain.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.size).to.equal(0);
    });

    it("should handle mixed static and dynamic domains", async () => {
        const staticDomain = await factory.create(FACTORY.STATIC_DOMAIN, {
            domain: "mixed-static-domain.com"
        });

        const dynamicDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "mixed-dynamic-domain.com",
            environment: "test"
        });

        const staticDomainPool = await factory.create(FACTORY.STATIC_DOMAIN_POOL, {
            name: "test-mixed-pool",
            domainDetectorAdapterId: "tapking"
        });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            staticDomainId: staticDomain.id,
            dynamicDomainId: dynamicDomain.id,
            staticDomainPoolId: staticDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("mixed-static-domain.com")).to.be.true;
        expect(tapkingDomains?.has("mixed-dynamic-domain.com")).to.be.true;
    });

    it("should skip inactive domains from pools", async () => {
        const activeDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "active-dynamic-domain.com",
            environment: "test"
        });
        const inactiveDomain = await factory.create(FACTORY.DYNAMIC_DOMAIN, {
            domain: "inactive-dynamic-domain.com",
            environment: "test"
        });

        const dynamicDomainPool = await factory.create(FACTORY.DYNAMIC_DOMAIN_POOL, {
            name: "test-active-inactive-pool",
            domainDetectorAdapterId: "tapking"
        });

        const dynamicDomainPoolService = require("../../../skywind/services/dynamicDomainPool").getDynamicDomainPoolService();
        await dynamicDomainPoolService.addDomain(dynamicDomainPool.id, activeDomain.id, { isActive: true });
        await dynamicDomainPoolService.addDomain(dynamicDomainPool.id, inactiveDomain.id, { isActive: false });

        await factory.create(FACTORY.ENTITY, {
            status: EntityStatus.NORMAL,
            dynamicDomainPoolId: dynamicDomainPool.id
        });

        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.has("tapking")).to.be.true;

        const tapkingDomains = result.get("tapking");
        expect(tapkingDomains).to.be.instanceOf(Map);
        expect(tapkingDomains?.has("active-dynamic-domain.com")).to.be.true;
        expect(tapkingDomains?.has("inactive-dynamic-domain.com")).to.be.false;
    });

    it("should return empty map when no entities found", async () => {
        const result = await loadDomains();

        expect(result).to.be.instanceOf(Map);
        expect(result.size).to.equal(0);
    });
});
